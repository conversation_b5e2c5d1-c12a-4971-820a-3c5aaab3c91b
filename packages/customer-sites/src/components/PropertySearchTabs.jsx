import SearchIcon from "@platform/assets/icons/search-01.svg?react";
import { useCallback, useEffect, useRef, useState } from "react";
import { PropertyLocationSelect } from "./PropertyLocationSelect.jsx";
import { PropertyTypeSelect } from "./PropertyTypeSelect.jsx";

export default function PropertySearchTabs({ primaryColor, headingColor, teamId, websiteAddress }) {
  const [activeTab, setActiveTab] = useState("alugar");
  const [tabMeasurements, setTabMeasurements] = useState({});
  const [indicatorStyle, setIndicatorStyle] = useState({});
  const containerRef = useRef(null);
  const alugarRef = useRef(null);
  const comprarRef = useRef(null);

  // Measure tab positions and update indicator
  const measureTabs = useCallback(() => {
    if (alugarRef.current && comprarRef.current && containerRef.current) {
      // Find the inner tab container (the one with the background)
      const tabContainer = containerRef.current.querySelector("[data-tab-container]");
      if (!tabContainer) return false;

      const tabContainerRect = tabContainer.getBoundingClientRect();
      const alugarRect = alugarRef.current.getBoundingClientRect();
      const comprarRect = comprarRef.current.getBoundingClientRect();

      // Check if elements have valid dimensions (not zero)
      if (alugarRect.width > 0 && comprarRect.width > 0 && tabContainerRect.width > 0) {
        const measurements = {
          alugar: {
            left: alugarRect.left - tabContainerRect.left,
            width: alugarRect.width,
          },
          comprar: {
            left: comprarRect.left - tabContainerRect.left,
            width: comprarRect.width,
          },
        };

        setTabMeasurements(measurements);
        return true; // Success
      }
    }
    return false; // Failed
  }, []);

  // Measure tabs with retry logic
  const measureTabsWithRetry = useCallback(
    (retries = 3) => {
      const attempt = () => {
        const success = measureTabs();
        if (!success && retries > 0) {
          // Check if we're on a smaller screen (where the issue occurs)
          const isSmallScreen = window.innerWidth < 768; // md breakpoint
          const delay = isSmallScreen ? 100 : 50; // Longer delay for small screens

          // Retry after a delay
          setTimeout(() => measureTabsWithRetry(retries - 1), delay);
        }
      };

      // Use requestAnimationFrame to ensure DOM is ready
      requestAnimationFrame(attempt);
    },
    [measureTabs],
  );

  // Update indicator position when active tab changes
  useEffect(() => {
    if (tabMeasurements[activeTab]) {
      setIndicatorStyle({
        left: `${tabMeasurements[activeTab].left}px`,
        width: `${tabMeasurements[activeTab].width}px`,
      });
    }
  }, [activeTab, tabMeasurements]);

  // Measure tabs on mount and when window resizes
  useEffect(() => {
    const handleResize = () => {
      const isSmallScreen = window.innerWidth < 768;

      if (isSmallScreen) {
        // For small screens, wait a bit longer and retry more times
        setTimeout(() => {
          measureTabsWithRetry(5); // More retries for small screens
        }, 150); // Initial delay for small screens
      } else {
        measureTabsWithRetry(3); // Normal behavior for larger screens
      }
    };

    // Initial measurement with small screen awareness
    handleResize();

    window.addEventListener("resize", handleResize);

    return () => window.removeEventListener("resize", handleResize);
  }, [measureTabsWithRetry]);

  const handleTabClick = (tab) => {
    setActiveTab(tab);
    // Update global variable for search functionality
    window.__selectedTransactionType = tab;

    // Trigger measurement if we don't have valid measurements yet
    if (!tabMeasurements[tab] || tabMeasurements[tab].width === 0) {
      const isSmallScreen = window.innerWidth < 768;
      if (isSmallScreen) {
        measureTabsWithRetry(3); // More retries for small screens
      } else {
        measureTabsWithRetry(1);
      }
    }
  };

  const handleSearch = () => {
    const selectedLocation = window.__selectedLocation;
    const selectedPropertyType = window.__selectedPropertyType;
    const transactionType = activeTab;

    let searchUrl = `/${websiteAddress}/imoveis`;
    const params = new URLSearchParams();

    if (selectedLocation && selectedLocation !== "all") {
      params.append("location", selectedLocation);
    }
    if (selectedPropertyType && selectedPropertyType !== "all") {
      params.append("type", selectedPropertyType);
    }
    if (transactionType) {
      params.append("transaction", transactionType);
    }

    if (params.toString()) {
      searchUrl += `?${params.toString()}`;
    }

    window.location.href = searchUrl;
  };

  return (
    <div className="mx-auto w-full max-w-md">
      {/* Tab Container */}
      <div ref={containerRef} className="relative mb-6 flex items-center justify-center gap-2">
        <div className="rounded-full bg-black/10 backdrop-blur-md" data-tab-container>
          {/* Morphing Background */}
          <div
            className="absolute rounded-full transition-all duration-300 ease-in-out"
            style={{
              backgroundColor: primaryColor,
              height: "100%",
              ...indicatorStyle,
            }}
          />

          {/* Tab Buttons */}
          <button
            ref={alugarRef}
            type="button"
            className="relative z-10 cursor-pointer rounded-md px-4 py-3 font-bold text-sm text-white transition-all duration-300 ease-in-out focus:outline-none"
            onClick={() => handleTabClick("alugar")}
          >
            Alugar
          </button>
          <button
            ref={comprarRef}
            type="button"
            className="relative z-10 cursor-pointer rounded-md px-4 py-3 font-bold text-sm text-white transition-all duration-300 ease-in-out focus:outline-none"
            onClick={() => handleTabClick("comprar")}
          >
            Comprar
            <SearchIcon className="h-5 w-5" />
          </button>
        </div>
      </div>

      {/* Search Fields */}
      <div className="flex flex-row items-center justify-between gap-4 rounded-full bg-background p-2 shadow-[0_4px_24px_rgba(0,0,0,0.5)]">
        {/* Location Field */}
        <PropertyLocationSelect primaryColor={primaryColor} teamId={teamId} />

        {/* Property Type Field */}
        <PropertyTypeSelect primaryColor={primaryColor} teamId={teamId} />

        {/* Search Button */}
        <button
          type="button"
          className="h-[3.2rem] w-[3.2rem] rounded-full font-bold text-white transition-colors"
          style={{ backgroundColor: primaryColor }}
          onMouseOver={(e) => {
            e.target.style.backgroundColor = headingColor;
          }}
          onFocus={(e) => {
            e.target.style.backgroundColor = headingColor;
          }}
          onMouseOut={(e) => {
            e.target.style.backgroundColor = primaryColor;
          }}
          onBlur={(e) => {
            e.target.style.backgroundColor = primaryColor;
          }}
          onClick={handleSearch}
          aria-label="Search properties"
        >
          <SearchIcon className="h-5 w-5" />
        </button>
      </div>
    </div>
  );
}
